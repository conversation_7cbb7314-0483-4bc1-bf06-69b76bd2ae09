# Solana gRPC Monitor - Rust版本

这是原TypeScript版本的Rust重写，用于监听Solana区块链上的SOL转账交易，特别关注新钱包的创建。

## 问题诊断

基于您的原始TypeScript代码，gRPC不来数据的可能原因包括：

### 1. gRPC端点问题
- **端点不可用**: `ny.vision-node.com:10000` 可能宕机或不可访问
- **网络问题**: 防火墙、代理或网络配置阻止连接
- **认证问题**: 端点可能需要API密钥或认证token

### 2. 订阅配置问题
- **过于宽泛的订阅**: 空的过滤条件可能导致服务端限制
- **服务端限制**: gRPC服务可能对连接数或数据量有限制

### 3. 过滤条件过于严格
```typescript
if (solAmount < 0.1) return;  // 过滤小于0.1 SOL的交易
if (solAmount > 100) return;  // 过滤大于100 SOL的交易
```
大部分SOL转账可能不在0.1-100 SOL范围内。

### 4. 数据结构解析问题
- gRPC返回的数据结构可能与代码假设不匹配
- 缺少调试日志来确认是否接收到原始数据

## 解决方案

### 步骤1: 运行调试版本
```bash
# 使用调试配置
cp Cargo_debug.toml Cargo.toml
cargo run --bin debug
```

这将：
- 测试gRPC连接
- 显示接收到的消息类型
- 提供详细的调试信息
- 在30秒内显示前10条消息

### 步骤2: 运行完整版本
```bash
# 使用完整配置
cp Cargo.toml.backup Cargo.toml  # 如果有备份
cargo run
```

## 主要改进

### 1. 更好的错误处理
```rust
// 自动重连机制
loop {
    match SolanaMonitor::new().await {
        Ok(mut monitor) => {
            if let Err(e) = monitor.start_monitoring().await {
                error!("Monitor error: {}", e);
                tokio::time::sleep(Duration::from_secs(5)).await;
            }
        }
        Err(e) => {
            error!("Failed to initialize: {}", e);
            tokio::time::sleep(Duration::from_secs(10)).await;
        }
    }
}
```

### 2. 详细的调试日志
```rust
info!("📨 Received message #{}: {:?}", message_count, update_type);
info!("  📋 Transaction update received");
info!("    Slot: {}", transaction.slot);
info!("    Signature: {}", signature);
```

### 3. 超时处理
```rust
tokio::select! {
    message = stream.next() => {
        // 处理消息
    }
    _ = timeout_future => {
        error!("⏰ Timeout: No messages received");
    }
}
```

### 4. 更健壮的指令解析
```rust
// 手动解析转账指令，避免依赖库的问题
let lamports_bytes: [u8; 8] = instruction.data[4..12].try_into()?;
let lamports = u64::from_le_bytes(lamports_bytes);
```

## 诊断建议

### 1. 检查网络连接
```bash
# 测试端点可达性
curl -v http://ny.vision-node.com:10000
telnet ny.vision-node.com 10000
```

### 2. 尝试其他端点
如果当前端点不工作，可以尝试：
- `https://api.mainnet-beta.solana.com` (官方RPC)
- 其他Yellowstone gRPC提供商
- 本地Solana节点

### 3. 放宽过滤条件
临时修改过滤条件进行测试：
```rust
// 临时注释掉金额过滤
// if sol_amount < 0.1 || sol_amount > 100.0 {
//     return Ok(());
// }
```

### 4. 检查数据库连接
确保PostgreSQL数据库可访问且表结构正确：
```sql
-- 检查表是否存在
SELECT * FROM information_schema.tables 
WHERE table_name IN ('wallet_transfer_logs', 'wallet_second_filter');
```

## 运行要求

- Rust 1.70+
- PostgreSQL数据库
- 网络访问权限

## 配置

在`src/main.rs`中修改：
- 数据库连接字符串
- gRPC端点URL
- 过滤条件（金额范围、新钱包阈值等）

## 故障排除

1. **编译错误**: 确保所有依赖版本兼容
2. **连接超时**: 检查网络和防火墙设置
3. **数据库错误**: 验证连接字符串和表结构
4. **无数据**: 运行调试版本确认连接状态

## 性能优化

- 使用连接池管理数据库连接
- 批量插入数据库记录
- 异步处理消息以避免阻塞
- 实现背压控制防止内存溢出
