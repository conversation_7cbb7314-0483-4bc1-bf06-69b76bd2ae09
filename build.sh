#!/bin/bash

echo "🦀 Solana gRPC Monitor - Rust版本构建脚本"
echo "=========================================="

# 检查Rust是否安装
if ! command -v cargo &> /dev/null; then
    echo "❌ Cargo未找到，请先安装Rust"
    echo "访问 https://rustup.rs/ 安装Rust"
    exit 1
fi

echo "✅ Rust版本: $(rustc --version)"

# 选择构建模式
echo ""
echo "请选择构建模式:"
echo "1) 简单连接测试 (推荐用于诊断)"
echo "2) 调试版本 (详细日志)"
echo "3) 完整版本 (包含数据库)"

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "🔧 构建简单连接测试..."
        cat > Cargo.toml << EOF
[package]
name = "solana-grpc-test"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "simple_test"
path = "src/simple_test.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
yellowstone-grpc-client = "1.13"
yellowstone-grpc-proto = "1.13"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
futures = "0.3"
EOF
        cargo build --bin simple_test
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行测试: cargo run --bin simple_test"
        fi
        ;;
    2)
        echo "🔧 构建调试版本..."
        cp Cargo_debug.toml Cargo.toml
        cargo build --bin debug
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行调试: cargo run --bin debug"
        fi
        ;;
    3)
        echo "🔧 构建完整版本..."
        cargo build
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行程序: cargo run"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔍 故障排除提示:"
echo "- 如果编译失败，尝试: cargo clean && cargo build"
echo "- 如果依赖问题，尝试: cargo update"
echo "- 如果网络问题，检查防火墙和代理设置"
echo "- 查看 README.md 获取更多帮助"
