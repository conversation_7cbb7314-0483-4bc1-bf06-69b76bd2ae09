use anyhow::Result;
use futures::StreamExt;
use tracing::{error, info, warn};
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::prelude::*;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    info!("🔍 Solana gRPC 连接测试");
    info!("这个简单测试将帮助诊断连接问题");

    // 测试不同的端点
    let endpoints = vec![
        "http://ny.vision-node.com:10000",
        "https://ny.vision-node.com:10000", 
        "http://ny.vision-node.com:443",
        "https://ny.vision-node.com:443",
    ];

    for endpoint in endpoints {
        info!("🌐 测试端点: {}", endpoint);
        
        match test_endpoint(endpoint).await {
            Ok(_) => {
                info!("✅ 端点 {} 连接成功!", endpoint);
                return Ok(());
            }
            Err(e) => {
                warn!("❌ 端点 {} 连接失败: {}", endpoint, e);
            }
        }
    }

    error!("❌ 所有端点都连接失败");
    error!("🔍 可能的原因:");
    error!("1. 网络连接问题");
    error!("2. 防火墙阻止连接");
    error!("3. gRPC服务不可用");
    error!("4. 需要认证或API密钥");
    
    Ok(())
}

async fn test_endpoint(endpoint: &str) -> Result<()> {
    // 尝试连接
    let client = GeyserGrpcClient::connect(endpoint, None, None).await?;
    info!("  ✅ gRPC连接建立成功");

    // 创建简单的ping请求
    let ping_request = SubscribeRequest {
        ping: Some(Ping { id: 1 }),
        ..Default::default()
    };

    let (mut tx, mut stream) = client.subscribe().await?;
    info!("  ✅ 订阅流创建成功");

    // 发送ping
    tx.send(ping_request).await?;
    info!("  ✅ Ping请求发送成功");

    // 等待响应
    let timeout = tokio::time::Duration::from_secs(10);
    match tokio::time::timeout(timeout, stream.next()).await {
        Ok(Some(Ok(response))) => {
            info!("  ✅ 收到响应: {:?}", response.update_oneof.as_ref().map(|u| std::mem::discriminant(u)));
            Ok(())
        }
        Ok(Some(Err(e))) => {
            Err(anyhow::anyhow!("Stream error: {}", e))
        }
        Ok(None) => {
            Err(anyhow::anyhow!("Stream ended unexpectedly"))
        }
        Err(_) => {
            Err(anyhow::anyhow!("Timeout waiting for response"))
        }
    }
}
