use anyhow::{Context, Result};
use futures::StreamExt;
use solana_sdk::{
    instruction::CompiledInstruction,
    message::VersionedMessage,
    pubkey::Pubkey,
    system_instruction::SystemInstruction,
    system_program,
};
use tracing::{debug, error, info, warn};
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::prelude::*;

struct DebugMonitor {
    grpc_client: GeyserGrpcClient<tonic::transport::channel::Channel>,
}

impl DebugMonitor {
    async fn new() -> Result<Self> {
        info!("Attempting to connect to gRPC endpoint...");
        
        // 尝试连接到gRPC端点
        let endpoint = "http://ny.vision-node.com:10000";
        info!("Connecting to: {}", endpoint);
        
        let grpc_client = GeyserGrpcClient::connect(endpoint, None, None)
            .await
            .context("Failed to connect to gRPC endpoint")?;

        info!("✅ gRPC connection successful!");
        
        Ok(Self { grpc_client })
    }

    async fn test_connection(&mut self) -> Result<()> {
        info!("Testing gRPC connection with simple subscription...");

        // 创建一个简单的订阅请求
        let mut subscribe_request = SubscribeRequest {
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: vec![],
            ping: None,
            ..Default::default()
        };

        // 配置交易订阅 - 更宽松的配置
        subscribe_request.transactions.insert(
            "client".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                signature: None,
                account_include: vec![],
                account_exclude: vec![],
                account_required: vec![],
            },
        );

        info!("Sending subscription request...");
        let (mut subscribe_tx, mut stream) = self.grpc_client.subscribe().await?;
        
        subscribe_tx.send(subscribe_request).await
            .context("Failed to send subscription request")?;
        
        info!("✅ Subscription request sent successfully!");
        info!("Waiting for data... (will show first 10 messages)");

        let mut message_count = 0;
        let max_messages = 10;

        // 设置超时
        let timeout = tokio::time::Duration::from_secs(30);
        let timeout_future = tokio::time::sleep(timeout);
        tokio::pin!(timeout_future);

        loop {
            tokio::select! {
                message = stream.next() => {
                    match message {
                        Some(Ok(update)) => {
                            message_count += 1;
                            info!("📨 Received message #{}: {:?}", message_count, update.update_oneof.as_ref().map(|u| std::mem::discriminant(u)));
                            
                            // 详细分析消息内容
                            self.analyze_message(update).await;
                            
                            if message_count >= max_messages {
                                info!("✅ Successfully received {} messages. Connection is working!", max_messages);
                                break;
                            }
                        }
                        Some(Err(e)) => {
                            error!("❌ Stream error: {}", e);
                            return Err(anyhow::anyhow!("Stream error: {}", e));
                        }
                        None => {
                            warn!("🔌 Stream ended unexpectedly");
                            break;
                        }
                    }
                }
                _ = &mut timeout_future => {
                    if message_count == 0 {
                        error!("⏰ Timeout: No messages received in {} seconds", timeout.as_secs());
                        error!("This suggests the gRPC connection is established but no data is flowing");
                        return Err(anyhow::anyhow!("No data received within timeout"));
                    } else {
                        info!("⏰ Timeout reached, but received {} messages", message_count);
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    async fn analyze_message(&self, update: SubscribeUpdate) {
        match update.update_oneof {
            Some(SubscribeUpdateOneof::Transaction(tx_update)) => {
                info!("  📋 Transaction update received");
                if let Some(transaction) = tx_update.transaction {
                    info!("    Slot: {}", transaction.slot);
                    if let Some(tx) = transaction.transaction {
                        if let Some(meta) = tx.meta {
                            info!("    Pre-balances count: {}", meta.pre_balances.len());
                            info!("    Post-balances count: {}", meta.post_balances.len());
                            info!("    Error: {:?}", meta.err);
                        }
                        if let Some(tx_data) = tx.transaction {
                            info!("    Signature: {}", bs58::encode(&tx_data.signature).into_string());
                            if let Some(message) = tx_data.message {
                                match message {
                                    Message::Legacy(legacy) => {
                                        info!("    Message type: Legacy");
                                        info!("    Instructions count: {}", legacy.instructions.len());
                                    }
                                    Message::V0(v0) => {
                                        info!("    Message type: V0");
                                        info!("    Instructions count: {}", v0.instructions.len());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Some(SubscribeUpdateOneof::Account(account_update)) => {
                info!("  💰 Account update received");
            }
            Some(SubscribeUpdateOneof::Slot(slot_update)) => {
                info!("  🎰 Slot update received: {:?}", slot_update);
            }
            Some(SubscribeUpdateOneof::Block(block_update)) => {
                info!("  🧱 Block update received");
            }
            Some(SubscribeUpdateOneof::Ping(ping)) => {
                info!("  🏓 Ping received: {:?}", ping);
            }
            Some(SubscribeUpdateOneof::Pong(pong)) => {
                info!("  🏓 Pong received: {:?}", pong);
            }
            Some(SubscribeUpdateOneof::BlockMeta(block_meta)) => {
                info!("  📊 Block meta received");
            }
            Some(SubscribeUpdateOneof::Entry(entry)) => {
                info!("  📝 Entry received");
            }
            None => {
                info!("  ❓ Unknown message type received");
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志 - 设置为debug级别以获取更多信息
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    info!("🚀 Starting Solana gRPC Debug Monitor");
    info!("This will help diagnose why no data is coming through");

    match DebugMonitor::new().await {
        Ok(mut monitor) => {
            info!("✅ Monitor initialized successfully");
            
            if let Err(e) = monitor.test_connection().await {
                error!("❌ Connection test failed: {}", e);
                
                // 提供诊断建议
                error!("🔍 Diagnostic suggestions:");
                error!("1. Check if the gRPC endpoint 'ny.vision-node.com:10000' is accessible");
                error!("2. Verify network connectivity and firewall settings");
                error!("3. Check if the endpoint requires authentication");
                error!("4. Try using a different endpoint or port");
                error!("5. Check if the yellowstone-grpc service is running");
                
                return Err(e);
            }
        }
        Err(e) => {
            error!("❌ Failed to initialize monitor: {}", e);
            error!("🔍 This usually means:");
            error!("1. The gRPC endpoint is not reachable");
            error!("2. Network connectivity issues");
            error!("3. The endpoint URL is incorrect");
            error!("4. The service is down or not responding");
            return Err(e);
        }
    }

    info!("🎉 Debug session completed successfully!");
    Ok(())
}
