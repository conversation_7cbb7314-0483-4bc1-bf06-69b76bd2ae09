[package]
name = "solana-grpc-monitor"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tonic = "0.11"
prost = "0.12"
yellowstone-grpc-client = "1.13"
yellowstone-grpc-proto = "1.13"
solana-sdk = "1.17"
bs58 = "0.5"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
futures = "0.3"
